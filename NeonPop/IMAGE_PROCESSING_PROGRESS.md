# 图片处理进度条功能

## 功能概述

为 NeonPop 应用添加了图片加载和抠图处理的进度条功能，解决了用户在导入图片时看不到处理状态的问题。

## 新增功能

### 1. 图片处理进度状态管理

在 `MultiLayerProcessor` 中添加了以下属性：
- `@Published var isProcessingImage: Bool` - 是否正在处理图片
- `@Published var imageProcessingProgress: Double` - 处理进度 (0.0-1.0)
- `@Published var imageProcessingStep: String` - 当前处理步骤描述

### 2. 进度显示界面

创建了 `ImageProcessingView` 组件，提供：
- 赛博朋克风格的动画效果
- 实时进度条显示
- 当前处理步骤文字说明
- 取消按钮（可中断处理）

### 3. 处理步骤细分

图片处理过程被分为以下步骤：
1. **正在加载图片...** (0-20%)
2. **正在分析图片...** (20-40%)
3. **正在移除背景...** (40-90%)
4. **正在完成处理...** (90-100%)

### 4. 重试功能进度

手动重试抠图功能也添加了进度显示：
1. **正在重新分析图片...** (0-40%)
2. **正在重新移除背景...** (40-90%)
3. **正在完成处理...** (90-100%)

## 技术实现

### MultiLayerProcessor 更新

```swift
// 添加图像图层时显示进度
func addImageLayer(from image: UIImage, name: String? = nil) async {
    // 开始处理进度
    isProcessingImage = true
    imageProcessingProgress = 0.0
    imageProcessingStep = "正在加载图片..."
    
    // ... 处理逻辑，在各个阶段更新进度
    
    // 完成处理
    imageProcessingProgress = 1.0
    imageProcessingStep = "处理完成"
    
    // 延迟隐藏进度条
    try? await Task.sleep(nanoseconds: 500_000_000)
    isProcessingImage = false
}
```

### ImageProcessingView 组件

- 使用与 `VideoProcessingView` 相似的设计风格
- 包含旋转动画、脉冲效果和粒子动画
- 实时显示处理进度和步骤描述
- 支持用户取消操作

### ContentView 集成

在主界面中添加了进度视图的 overlay：

```swift
.overlay(
    Group {
        if processor.isProcessingImage {
            ImageProcessingView(
                processor: processor,
                onCancel: {
                    processor.isProcessingImage = false
                }
            )
        }
    }
)
```

## 用户体验改进

### 之前的问题
- 用户导入图片时没有任何反馈
- 不知道是否正在处理或已经卡住
- 抠图过程对用户来说是黑盒操作

### 现在的体验
- 清晰的进度指示和步骤说明
- 美观的动画效果保持界面一致性
- 可以取消正在进行的处理
- 重试抠图时也有进度反馈

## 视觉设计

### 动画效果
- 外圈旋转环：电蓝色到霓虹粉的渐变
- 内圈脉冲：霓虹粉色的呼吸效果
- 粒子动画：围绕中心旋转的小圆点
- 中心图标：`photo.badge.waveform` 表示图片处理

### 颜色方案
- 背景：半透明黑色遮罩
- 主色调：电蓝色 (`CyberPunkStyle.electricBlue`)
- 强调色：霓虹粉 (`CyberPunkStyle.neonPink`)
- 文字：白色及其透明度变体

## 错误处理

- 如果抠图失败，进度条会显示相应状态
- 用户可以通过取消按钮中断处理
- 处理完成后自动隐藏进度界面

## 性能考虑

- 进度更新在主线程进行，确保 UI 响应
- 使用 `Task.sleep` 避免进度条闪烁
- 异步处理不阻塞用户界面

## 未来扩展

可以考虑添加：
- 更详细的处理步骤分解
- 估计剩余时间显示
- 处理速度优化提示
- 批量处理进度支持
