//
//  ImageProcessingView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/20.
//

import SwiftUI

struct ImageProcessingView: View {
    @ObservedObject var processor: MultiLayerProcessor
    let onCancel: () -> Void
    
    var body: some View {
        ZStack {
            // 背景遮罩
            Color.black.opacity(0.8)
                .ignoresSafeArea()
            
            VStack(spacing: 30) {
                // 处理动画
                processingAnimation
                
                // 状态信息
                VStack(spacing: 12) {
                    Text("图片处理中")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(CyberPunkStyle.neonPink)
                    
                    Text(processor.imageProcessingStep)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                }
                
                // 进度条
                VStack(spacing: 8) {
                    ProgressView(value: processor.imageProcessingProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: CyberPunkStyle.electricBlue))
                        .scaleEffect(x: 1, y: 2, anchor: .center)
                        .frame(width: 250)
                    
                    Text("\(Int(processor.imageProcessingProgress * 100))%")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
                
                // 取消按钮
                Button("取消") {
                    onCancel()
                }
                .foregroundColor(.white)
                .padding(.horizontal, 30)
                .padding(.vertical, 12)
                .background(Color.gray.opacity(0.3))
                .cornerRadius(8)
            }
            .padding()
        }
    }
    
    private var processingAnimation: some View {
        ZStack {
            // 外圈旋转环
            Circle()
                .stroke(
                    LinearGradient(
                        colors: [
                            CyberPunkStyle.electricBlue,
                            CyberPunkStyle.neonPink,
                            CyberPunkStyle.electricBlue
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 4
                )
                .frame(width: 120, height: 120)
                .rotationEffect(.degrees(rotationAngle))
                .shadow(color: CyberPunkStyle.electricBlue.opacity(0.6), radius: 10)
            
            // 内圈脉冲
            Circle()
                .fill(CyberPunkStyle.neonPink.opacity(0.3))
                .frame(width: 80, height: 80)
                .scaleEffect(pulseScale)
                .opacity(pulseOpacity)
            
            // 中心图标
            Image(systemName: "photo.badge.waveform")
                .font(.system(size: 30, weight: .medium))
                .foregroundColor(.white)
                .shadow(color: .white.opacity(0.8), radius: 5)
            
            // 处理粒子效果
            ForEach(0..<6, id: \.self) { index in
                processingParticle(for: index)
            }
        }
        .onAppear {
            startAnimations()
        }
    }
    
    @State private var rotationAngle: Double = 0
    @State private var pulseScale: CGFloat = 1.0
    @State private var pulseOpacity: Double = 0.3
    
    private func startAnimations() {
        // 旋转动画
        withAnimation(.linear(duration: 3).repeatForever(autoreverses: false)) {
            rotationAngle = 360
        }
        
        // 脉冲动画
        withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
            pulseScale = 1.3
            pulseOpacity = 0.1
        }
    }
    
    private func processingParticle(for index: Int) -> some View {
        let angle = Double(index) * 60.0 // 每个粒子间隔60度
        let radius: CGFloat = 70
        
        return Circle()
            .fill(CyberPunkStyle.electricBlue)
            .frame(width: 4, height: 4)
            .offset(
                x: cos(Angle(degrees: angle + rotationAngle * 2).radians) * radius,
                y: sin(Angle(degrees: angle + rotationAngle * 2).radians) * radius
            )
            .opacity(0.8)
            .shadow(color: CyberPunkStyle.electricBlue, radius: 2)
    }
}

// 图片处理状态枚举
enum ImageProcessingStep {
    case loading
    case analyzing
    case removingBackground
    case finalizing
    case completed
    case failed
    
    var description: String {
        switch self {
        case .loading:
            return "正在加载图片..."
        case .analyzing:
            return "正在分析图片..."
        case .removingBackground:
            return "正在移除背景..."
        case .finalizing:
            return "正在完成处理..."
        case .completed:
            return "处理完成"
        case .failed:
            return "处理失败"
        }
    }
}

#Preview {
    let processor = MultiLayerProcessor()
    processor.isProcessingImage = true
    processor.imageProcessingProgress = 0.6
    processor.imageProcessingStep = "正在移除背景..."
    
    return ImageProcessingView(
        processor: processor,
        onCancel: { }
    )
}
