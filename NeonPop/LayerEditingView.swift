//
//  LayerEditingView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/16.
//

import SwiftUI

struct LayerEditingView: View {
    @ObservedObject var layer: LayerModel
    @ObservedObject var processor: MultiLayerProcessor
    @ObservedObject var undoManager: UndoRedoManager

    // 添加文字编辑相关状态
    @State private var showingTextEditor = false
    @State private var isInlineEditing = false
    
    @State private var dragOffset: CGSize = .zero
    @State private var lastDragOffset: CGSize = .zero
    @State private var scale: CGFloat = 1.0
    @State private var lastScale: CGFloat = 1.0
    @State private var rotation: Double = 0.0
    @State private var lastRotation: Double = 0.0
    
    let canvasSize: CGSize
    
    var body: some View {
        Group {
            if layer.type == .image {
                imageLayerView
            } else if layer.type == .text {
                // 使用新的文字变换控制器
                TextTransformController(
                    layer: layer,
                    processor: processor,
                    undoManager: undoManager,
                    canvasSize: canvasSize
                )
            } else if layer.type == .video {
                videoLayerView
            }
        }
        .sheet(isPresented: $showingTextEditor) {
            if layer.type == .text {
                TextEditingView(layer: layer, processor: processor, undoManager: undoManager)
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .startInlineEditing)) { notification in
            if let editingLayer = notification.object as? LayerModel,
               editingLayer.id == layer.id {
                isInlineEditing = true
            }
        }
    }

    // 视频图层视图
    private var videoLayerView: some View {
        VideoLayerView(layer: layer)
            .frame(width: getVideoDisplaySize().width, height: getVideoDisplaySize().height)
            .scaleEffect(scale * layer.transform.scale)
            .rotationEffect(.degrees(rotation + layer.transform.rotation))
            .offset(x: dragOffset.width + layer.transform.position.x,
                    y: dragOffset.height + layer.transform.position.y)
            .opacity(layer.transform.opacity)
            .overlay(
                // 选择框
                Group {
                    if layer.isSelected {
                        Rectangle()
                            .stroke(CyberPunkStyle.neonPink, lineWidth: 2)
                            .background(Color.clear)
                    }
                }
            )
            .gesture(
                SimultaneousGesture(
                    dragGesture,
                    SimultaneousGesture(
                        magnificationGesture,
                        rotationGesture
                    )
                )
            )
            .onTapGesture {
                // 切换选中状态：如果已选中则取消选中，否则选中
                if layer.isSelected {
                    processor.deselectAllLayers()
                } else {
                    processor.selectLayer(layer)
                }
            }
    }

    // 获取视频显示尺寸
    private func getVideoDisplaySize() -> CGSize {
        guard let thumbnail = layer.videoThumbnail else {
            return CGSize(width: 200, height: 150)
        }

        let imageSize = thumbnail.size
        let maxDimension = max(canvasSize.width, canvasSize.height) * 0.8
        let scale = min(maxDimension / imageSize.width, maxDimension / imageSize.height)

        return CGSize(
            width: imageSize.width * scale,
            height: imageSize.height * scale
        )
    }

    // 图像图层视图
    private var imageLayerView: some View {
        ZStack {
            Group {
                if layer.type == .video && !layer.frameSequence.isEmpty {
                    // 视频图层：显示当前帧
                    let currentFrame = layer.frameSequence[layer.currentFrameIndex]
                    Image(uiImage: currentFrame)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .colorMultiply(layer.isColorTransformed ? CyberPunkStyle.neonPink : .white)
                        .animation(.easeInOut(duration: 0.3), value: layer.isColorTransformed)
                } else if let image = layer.processedImage ?? layer.originalImage {
                    // 普通图片图层
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        // 移除尺寸限制，允许图像以任意大小显示
                        .colorMultiply(layer.isColorTransformed ? CyberPunkStyle.neonPink : .white)
                        .animation(.easeInOut(duration: 0.3), value: layer.isColorTransformed)
                }
            }

            // 智能选中边框（只在选中时显示，基于实际图片内容大小）
            if layer.isSelected {
                smartSelectionBorder
            }
        }
        .scaleEffect(scale * layer.transform.scale)
        .rotationEffect(.degrees(rotation + layer.transform.rotation))
        .offset(x: dragOffset.width + layer.transform.position.x,
                y: dragOffset.height + layer.transform.position.y)
        .opacity(layer.transform.opacity)
        .gesture(
            SimultaneousGesture(
                dragGesture,
                SimultaneousGesture(
                    magnificationGesture,
                    rotationGesture
                )
            )
        )
        .onTapGesture {
            // 切换选中状态：如果已选中则取消选中，否则选中
            if layer.isSelected {
                processor.deselectAllLayers()
            } else {
                processor.selectLayer(layer)
            }
        }
    }

    // 智能选择边框 - 基于实际图片内容大小
    private var smartSelectionBorder: some View {
        Group {
            if let image = layer.processedImage ?? layer.originalImage {
                // 计算实际内容边界
                let contentBounds = calculateImageContentBounds(image)

                Rectangle()
                    .stroke(CyberPunkStyle.neonPink, lineWidth: 2)
                    .fill(Color.clear)
                    .frame(width: contentBounds.width, height: contentBounds.height)
                    .offset(x: contentBounds.offsetX, y: contentBounds.offsetY)
                    .shadow(color: CyberPunkStyle.neonPink.opacity(0.5), radius: 4)
                    .animation(.easeInOut(duration: 0.2), value: layer.isSelected)
            }
        }
    }

    // 计算图片实际内容边界
    private func calculateImageContentBounds(_ image: UIImage) -> (width: CGFloat, height: CGFloat, offsetX: CGFloat, offsetY: CGFloat) {
        // 检查是否是抠图后的图片
        let isProcessedImage = layer.processedImage != nil && layer.processedImage != layer.originalImage

        // 根据图片类型调整选择框大小
        let sizeMultiplier: CGFloat = isProcessedImage ? 0.7 : 1.0 // 抠图后的图片选择框更小
        let maxDimension: CGFloat = isProcessedImage ? 180 : 250 // 抠图后的最大尺寸更小

        let aspectRatio = image.size.width / image.size.height

        let width: CGFloat
        let height: CGFloat

        if aspectRatio > 1 {
            // 宽图
            width = min(maxDimension, image.size.width * sizeMultiplier)
            height = width / aspectRatio
        } else {
            // 高图或正方形
            height = min(maxDimension, image.size.height * sizeMultiplier)
            width = height * aspectRatio
        }

        // 添加最小尺寸限制，确保选择框不会太小
        let minSize: CGFloat = 60
        let finalWidth = max(width, minSize)
        let finalHeight = max(height, minSize)

        return (width: finalWidth, height: finalHeight, offsetX: 0, offsetY: 0)
    }
    
    // 文字图层视图
    private var textLayerView: some View {
        InlineTextEditor(
            layer: layer,
            processor: processor,
            undoManager: undoManager,
            isEditing: $isInlineEditing
        )
    }

    // 获取图层字体
    private func getLayerFont() -> Font {
        var font: Font

        if layer.fontName == "System" {
            font = .system(size: layer.fontSize)
        } else {
            font = .custom(layer.fontName, size: layer.fontSize)
        }

        if layer.isBold && layer.isItalic {
            return font.weight(.bold).italic()
        } else if layer.isBold {
            return font.weight(.bold)
        } else if layer.isItalic {
            return font.italic()
        }

        return font
    }

    // 获取文字对齐方式
    private func getTextAlignment() -> TextAlignment {
        return layer.textAlignment.swiftUIAlignment
    }
    

    
    // 拖拽手势
    private var dragGesture: some Gesture {
        DragGesture()
            .onChanged { value in
                // 开始拖拽时自动选中图层
                if !layer.isSelected {
                    processor.selectLayer(layer)
                }
                dragOffset = CGSize(
                    width: lastDragOffset.width + value.translation.width,
                    height: lastDragOffset.height + value.translation.height
                )
            }
            .onEnded { value in
                // 记录撤销操作
                let oldTransform = layer.transform

                // 计算新位置 - 移除边界限制，允许自由移动
                let newX = layer.transform.position.x + dragOffset.width
                let newY = layer.transform.position.y + dragOffset.height

                // 更新图层位置（无边界限制）
                layer.transform.position = CGPoint(x: newX, y: newY)

                // 记录新变换
                let newTransform = layer.transform
                undoManager.recordAction(.transformLayer(layer, oldTransform, newTransform))

                // 重置拖拽偏移
                lastDragOffset = .zero
                dragOffset = .zero

                // 更新合成图像
                processor.updateLayerTransform(layer, transform: layer.transform)
            }
    }
    
    // 缩放手势
    private var magnificationGesture: some Gesture {
        MagnificationGesture()
            .onChanged { value in
                // 开始缩放时自动选中图层
                if !layer.isSelected {
                    processor.selectLayer(layer)
                }
                scale = lastScale * value
            }
            .onEnded { value in
                // 记录撤销操作
                let oldTransform = layer.transform

                // 更新图层缩放 - 移除缩放限制，允许任意缩放
                layer.transform.scale *= scale

                // 只保留最小缩放限制以防止图层完全消失
                layer.transform.scale = max(0.01, layer.transform.scale)

                // 记录新变换
                let newTransform = layer.transform
                undoManager.recordAction(.transformLayer(layer, oldTransform, newTransform))

                // 重置缩放
                lastScale = 1.0
                scale = 1.0

                // 更新合成图像
                processor.updateLayerTransform(layer, transform: layer.transform)
            }
    }
    
    // 旋转手势
    private var rotationGesture: some Gesture {
        RotationGesture()
            .onChanged { value in
                // 开始旋转时自动选中图层
                if !layer.isSelected {
                    processor.selectLayer(layer)
                }
                rotation = lastRotation + value.degrees
            }
            .onEnded { value in
                // 记录撤销操作
                let oldTransform = layer.transform

                // 更新图层旋转
                layer.transform.rotation += rotation

                // 标准化角度到 0-360 度
                while layer.transform.rotation < 0 {
                    layer.transform.rotation += 360
                }
                while layer.transform.rotation >= 360 {
                    layer.transform.rotation -= 360
                }

                // 记录新变换
                let newTransform = layer.transform
                undoManager.recordAction(.transformLayer(layer, oldTransform, newTransform))

                // 重置旋转
                lastRotation = 0
                rotation = 0

                // 更新合成图像
                processor.updateLayerTransform(layer, transform: layer.transform)
            }
    }

    // 显示悬浮菜单
    private func showFloatingMenu() {
        // 计算菜单位置（在文字正上方）
        // 需要将图层坐标转换为屏幕坐标
        let screenCenter = CGPoint(x: canvasSize.width / 2, y: canvasSize.height / 2)
        let textScreenPosition = CGPoint(
            x: screenCenter.x + layer.transform.position.x,
            y: screenCenter.y + layer.transform.position.y
        )

        // 菜单显示在文字上方60点的位置
        let menuPosition = CGPoint(
            x: textScreenPosition.x,
            y: textScreenPosition.y - 60
        )

        processor.showFloatingMenu(for: layer, at: menuPosition)
    }
}

// 图层列表项视图
struct LayerListItemView: View {
    @ObservedObject var layer: LayerModel
    @ObservedObject var processor: MultiLayerProcessor
    @ObservedObject var undoManager: UndoRedoManager
    
    var body: some View {
        HStack(spacing: 12) {
            // 图层类型图标
            Image(systemName: layerIcon)
                .font(.system(size: 16))
                .foregroundColor(layer.isSelected ? CyberPunkStyle.neonPink : .white.opacity(0.7))
                .frame(width: 20)
            
            // 图层名称
            Text(layer.name)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(layer.isSelected ? CyberPunkStyle.neonPink : .white)
                .lineLimit(1)
            
            Spacer()
            
            // 可见性切换
            Button(action: {
                let oldVisibility = layer.isVisible
                undoManager.recordAction(.toggleLayerVisibility(layer, oldVisibility))
                processor.toggleLayerVisibility(layer)
            }) {
                Image(systemName: layer.isVisible ? "eye" : "eye.slash")
                    .font(.system(size: 14))
                    .foregroundColor(layer.isVisible ? .white.opacity(0.7) : .red.opacity(0.7))
            }
            
            // 锁定切换
            Button(action: {
                layer.isLocked.toggle()
            }) {
                Image(systemName: layer.isLocked ? "lock" : "lock.open")
                    .font(.system(size: 14))
                    .foregroundColor(layer.isLocked ? .orange.opacity(0.7) : .white.opacity(0.7))
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(layer.isSelected ? CyberPunkStyle.neonPink.opacity(0.1) : Color.clear)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(layer.isSelected ? CyberPunkStyle.neonPink : Color.clear, lineWidth: 1)
                )
        )
        .onTapGesture {
            processor.selectLayer(layer)
        }
    }
    
    private var layerIcon: String {
        switch layer.type {
        case .image:
            return "photo"
        case .text:
            return "textformat"
        case .effect:
            return "sparkles"
        case .video:
            return "video"
        }
    }
}
