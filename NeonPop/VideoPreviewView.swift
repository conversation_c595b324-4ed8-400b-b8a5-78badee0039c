//
//  VideoPreviewView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/20.
//

import SwiftUI

struct VideoPreviewView: View {
    let preview: VideoProcessor.FramePreview
    let onConfirm: () -> Void
    let onRetry: () -> Void
    let onCancel: () -> Void
    
    @State private var showingOriginal = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("抠图效果预览")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(CyberPunkStyle.neonPink)
                    .padding(.top)
                
                // 预览图片对比
                VStack(spacing: 16) {
                    // 切换按钮
                    HStack(spacing: 20) {
                        Button(action: { showingOriginal = false }) {
                            Text("抠图后")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(showingOriginal ? .white.opacity(0.6) : CyberPunkStyle.neonPink)
                                .padding(.horizontal, 20)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(showingOriginal ? Color.clear : CyberPunkStyle.neonPink.opacity(0.2))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 8)
                                                .stroke(showingOriginal ? Color.clear : CyberPunkStyle.neonPink, lineWidth: 1)
                                        )
                                )
                        }
                        
                        Button(action: { showingOriginal = true }) {
                            Text("原图")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(showingOriginal ? CyberPunkStyle.neonPink : .white.opacity(0.6))
                                .padding(.horizontal, 20)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(showingOriginal ? CyberPunkStyle.neonPink.opacity(0.2) : Color.clear)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 8)
                                                .stroke(showingOriginal ? CyberPunkStyle.neonPink : Color.clear, lineWidth: 1)
                                        )
                                )
                        }
                    }
                    
                    // 预览图片
                    ZStack {
                        // 背景网格（显示透明效果）
                        if !showingOriginal {
                            TransparencyGrid()
                        }
                        
                        Image(uiImage: showingOriginal ? preview.originalFrame : preview.processedFrame)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(maxHeight: 400)
                            .cornerRadius(12)
                            .shadow(color: .black.opacity(0.3), radius: 10)
                    }
                    .animation(.easeInOut(duration: 0.3), value: showingOriginal)
                }
                .padding(.horizontal)
                
                // 提示文字
                Text("请检查抠图效果是否满意")
                    .font(.system(size: 16))
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                Spacer()
                
                // 底部按钮
                VStack(spacing: 16) {
                    // 主要操作按钮
                    HStack(spacing: 20) {
                        Button("重新抠图") {
                            onRetry()
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(Color.orange.opacity(0.8))
                        .cornerRadius(8)
                        
                        Button("确认批量处理") {
                            onConfirm()
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(CyberPunkStyle.electricBlue)
                        .cornerRadius(8)
                    }
                    
                    // 取消按钮
                    Button("取消") {
                        onCancel()
                    }
                    .foregroundColor(.white.opacity(0.7))
                    .padding(.horizontal, 30)
                    .padding(.vertical, 10)
                    .background(Color.gray.opacity(0.3))
                    .cornerRadius(8)
                }
                .padding(.bottom)
            }
            .background(Color.black)
            .navigationBarHidden(true)
        }
    }
}

// 透明网格背景
struct TransparencyGrid: View {
    var body: some View {
        Canvas { context, size in
            let gridSize: CGFloat = 20
            let rows = Int(size.height / gridSize) + 1
            let cols = Int(size.width / gridSize) + 1
            
            for row in 0..<rows {
                for col in 0..<cols {
                    let isEven = (row + col) % 2 == 0
                    let rect = CGRect(
                        x: CGFloat(col) * gridSize,
                        y: CGFloat(row) * gridSize,
                        width: gridSize,
                        height: gridSize
                    )
                    
                    context.fill(
                        Path(rect),
                        with: .color(isEven ? .white.opacity(0.1) : .white.opacity(0.05))
                    )
                }
            }
        }
    }
}

#Preview {
    let preview = VideoProcessor.FramePreview(
        originalFrame: UIImage(systemName: "photo")!,
        processedFrame: UIImage(systemName: "photo.badge.plus")!,
        frameTime: 1.5
    )
    
    return VideoPreviewView(
        preview: preview,
        onConfirm: { },
        onRetry: { },
        onCancel: { }
    )
}
