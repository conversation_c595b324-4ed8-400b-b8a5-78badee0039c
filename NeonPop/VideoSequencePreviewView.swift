//
//  VideoSequencePreviewView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/20.
//

import SwiftUI

struct VideoSequencePreviewView: View {
    let frameSequence: VideoProcessor.VideoFrameSequence
    let onConfirm: () -> Void
    let onRetry: () -> Void
    let onCancel: () -> Void
    
    @State private var currentFrameIndex = 0
    @State private var isPlaying = false
    @State private var playbackTimer: Timer?
    @State private var showingOriginal = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("序列帧预览")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(CyberPunkStyle.neonPink)
                    .padding(.top)
                
                // 播放控制
                HStack(spacing: 20) {
                    Button(action: togglePlayback) {
                        Image(systemName: isPlaying ? "pause.circle.fill" : "play.circle.fill")
                            .font(.system(size: 40))
                            .foregroundColor(CyberPunkStyle.electricBlue)
                    }
                    
                    VStack(spacing: 4) {
                        Text("帧 \(currentFrameIndex + 1) / \(frameSequence.frames.count)")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                        
                        Text("\(frameSequence.frameRate, specifier: "%.1f") fps")
                            .font(.system(size: 14))
                            .foregroundColor(.white.opacity(0.7))
                    }
                    
                    Button("重置") {
                        stopPlayback()
                        currentFrameIndex = 0
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(Color.gray.opacity(0.3))
                    .cornerRadius(8)
                }
                
                // 预览区域
                VStack(spacing: 16) {
                    // 切换按钮
                    HStack(spacing: 20) {
                        Button(action: { showingOriginal = false }) {
                            Text("抠图后")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(showingOriginal ? .white.opacity(0.6) : CyberPunkStyle.neonPink)
                                .padding(.horizontal, 20)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(showingOriginal ? Color.clear : CyberPunkStyle.neonPink.opacity(0.2))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 8)
                                                .stroke(showingOriginal ? Color.clear : CyberPunkStyle.neonPink, lineWidth: 1)
                                        )
                                )
                        }
                        
                        Button(action: { showingOriginal = true }) {
                            Text("原始帧")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(showingOriginal ? CyberPunkStyle.neonPink : .white.opacity(0.6))
                                .padding(.horizontal, 20)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(showingOriginal ? CyberPunkStyle.neonPink.opacity(0.2) : Color.clear)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 8)
                                                .stroke(showingOriginal ? CyberPunkStyle.neonPink : Color.clear, lineWidth: 1)
                                        )
                                )
                        }
                    }
                    
                    // 预览帧
                    ZStack {
                        // 透明网格背景（仅抠图后显示）
                        if !showingOriginal {
                            TransparencyGrid()
                        }
                        
                        if currentFrameIndex < frameSequence.frames.count {
                            Image(uiImage: frameSequence.frames[currentFrameIndex])
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(maxHeight: 400)
                                .cornerRadius(12)
                                .shadow(color: .black.opacity(0.3), radius: 10)
                        }
                    }
                    .animation(.easeInOut(duration: 0.3), value: showingOriginal)
                }
                .padding(.horizontal)
                
                // 进度条
                VStack(spacing: 8) {
                    Slider(
                        value: Binding(
                            get: { Double(currentFrameIndex) },
                            set: { newValue in
                                stopPlayback()
                                currentFrameIndex = Int(newValue)
                            }
                        ),
                        in: 0...Double(frameSequence.frames.count - 1),
                        step: 1
                    )
                    .accentColor(CyberPunkStyle.electricBlue)
                    
                    Text("拖动查看不同帧")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                }
                .padding(.horizontal)
                
                // 提示文字
                Text("请检查抠图效果是否满意")
                    .font(.system(size: 16))
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                Spacer()
                
                // 底部按钮
                VStack(spacing: 16) {
                    // 主要操作按钮
                    HStack(spacing: 20) {
                        Button("重新处理") {
                            stopPlayback()
                            onRetry()
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(Color.orange.opacity(0.8))
                        .cornerRadius(8)
                        
                        Button("确认添加到画布") {
                            stopPlayback()
                            onConfirm()
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(CyberPunkStyle.electricBlue)
                        .cornerRadius(8)
                    }
                    
                    // 取消按钮
                    Button("取消") {
                        stopPlayback()
                        onCancel()
                    }
                    .foregroundColor(.white.opacity(0.7))
                    .padding(.horizontal, 30)
                    .padding(.vertical, 10)
                    .background(Color.gray.opacity(0.3))
                    .cornerRadius(8)
                }
                .padding(.bottom)
            }
            .background(Color.black)
            .navigationBarHidden(true)
        }
        .onDisappear {
            stopPlayback()
        }
    }
    
    private func togglePlayback() {
        if isPlaying {
            stopPlayback()
        } else {
            startPlayback()
        }
    }
    
    private func startPlayback() {
        stopPlayback() // 先停止现有定时器
        
        isPlaying = true
        let frameInterval = 1.0 / frameSequence.frameRate
        
        playbackTimer = Timer.scheduledTimer(withTimeInterval: frameInterval, repeats: true) { _ in
            DispatchQueue.main.async {
                self.currentFrameIndex = (self.currentFrameIndex + 1) % self.frameSequence.frames.count
            }
        }
    }
    
    private func stopPlayback() {
        isPlaying = false
        playbackTimer?.invalidate()
        playbackTimer = nil
    }
}

#Preview {
    let frameSequence = VideoProcessor.VideoFrameSequence(
        frames: [
            UIImage(systemName: "1.circle.fill")!,
            UIImage(systemName: "2.circle.fill")!,
            UIImage(systemName: "3.circle.fill")!
        ],
        frameRate: 2.0,
        duration: 1.5,
        originalSize: CGSize(width: 100, height: 100)
    )
    
    return VideoSequencePreviewView(
        frameSequence: frameSequence,
        onConfirm: { },
        onRetry: { },
        onCancel: { }
    )
}
