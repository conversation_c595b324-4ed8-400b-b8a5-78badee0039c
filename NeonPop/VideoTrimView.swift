//
//  VideoTrimView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/19.
//

import SwiftUI
import AVFoundation
import AVKit

struct VideoTrimView: View {
    let videoURL: URL
    let onComplete: (VideoProcessor.VideoFrameSequence) -> Void
    let onCancel: () -> Void

    @StateObject private var videoProcessor = VideoProcessor()
    @State private var player: AVPlayer?
    @State private var videoDuration: Double = 0
    @State private var startTime: Double = 0
    @State private var endTime: Double = 10
    @State private var currentTime: Double = 0
    @State private var isPlaying = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var showingProcessing = false
    @State private var showingPreview = false
    @State private var framePreview: VideoProcessor.FramePreview?

    private let maxDuration: Double = 10.0 // 最大10秒
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 视频预览
                if let player = player {
                    VideoPlayer(player: player)
                        .frame(height: 300)
                        .cornerRadius(12)
                        .onAppear {
                            setupPlayer()
                        }
                } else {
                    Rectangle()
                        .fill(Color.black)
                        .frame(height: 300)
                        .cornerRadius(12)
                        .overlay(
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        )
                }
                
                // 时间信息
                VStack(spacing: 8) {
                    HStack {
                        Text("视频总长度: \(formatTime(videoDuration))")
                        Spacer()
                        Text("选择长度: \(formatTime(endTime - startTime))")
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                    
                    HStack {
                        Text("开始: \(formatTime(startTime))")
                        Spacer()
                        Text("结束: \(formatTime(endTime))")
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                }
                .padding(.horizontal)
                
                // 时间轴控制
                VStack(spacing: 16) {
                    // 开始时间滑块
                    VStack(alignment: .leading, spacing: 4) {
                        Text("开始时间")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        HStack {
                            Text(formatTime(startTime))
                                .font(.caption)
                                .frame(width: 50, alignment: .leading)
                            
                            Slider(value: $startTime, in: 0...max(0, videoDuration - maxDuration)) { editing in
                                if !editing {
                                    updateEndTime()
                                    seekToTime(startTime)
                                }
                            }
                            .accentColor(Color(CyberPunkStyle.electricBlue))
                            
                            Text(formatTime(max(0, videoDuration - maxDuration)))
                                .font(.caption)
                                .frame(width: 50, alignment: .trailing)
                        }
                    }
                    
                    // 结束时间滑块
                    VStack(alignment: .leading, spacing: 4) {
                        Text("结束时间")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        HStack {
                            Text(formatTime(endTime))
                                .font(.caption)
                                .frame(width: 50, alignment: .leading)
                            
                            Slider(value: $endTime, in: min(startTime + 1, videoDuration)...videoDuration) { editing in
                                if !editing {
                                    updateStartTime()
                                    seekToTime(endTime)
                                }
                            }
                            .accentColor(Color(CyberPunkStyle.electricBlue))
                            
                            Text(formatTime(videoDuration))
                                .font(.caption)
                                .frame(width: 50, alignment: .trailing)
                        }
                    }
                }
                .padding(.horizontal)
                
                // 播放控制
                HStack(spacing: 20) {
                    Button(action: {
                        seekToTime(startTime)
                    }) {
                        Image(systemName: "backward.end")
                            .font(.title2)
                            .foregroundColor(.white)
                    }
                    
                    Button(action: togglePlayback) {
                        Image(systemName: isPlaying ? "pause.circle.fill" : "play.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                    }
                    
                    Button(action: {
                        seekToTime(endTime)
                    }) {
                        Image(systemName: "forward.end")
                            .font(.title2)
                            .foregroundColor(.white)
                    }
                }
                .padding()
                .background(Color.black.opacity(0.3))
                .cornerRadius(12)
                
                Spacer()
                
                // 底部按钮
                HStack(spacing: 20) {
                    Button("取消") {
                        onCancel()
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 30)
                    .padding(.vertical, 12)
                    .background(Color.gray.opacity(0.3))
                    .cornerRadius(8)
                    
                    Button("预览抠图") {
                        previewCutout()
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 30)
                    .padding(.vertical, 12)
                    .background(Color(CyberPunkStyle.electricBlue))
                    .cornerRadius(8)
                    .disabled(videoProcessor.isProcessing || showingProcessing)
                }
                .padding(.bottom)
            }
            .padding()
            .background(Color.black)
            .navigationTitle("视频截取")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("取消") { onCancel() })
        }
        .onAppear {
            loadVideoInfo()
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
        .overlay(
            Group {
                if showingProcessing {
                    VideoProcessingView(
                        videoProcessor: videoProcessor,
                        onCancel: {
                            showingProcessing = false
                            onCancel()
                        }
                    )
                }
            }
        )
        .sheet(isPresented: $showingPreview) {
            if let preview = framePreview {
                VideoPreviewView(
                    preview: preview,
                    onConfirm: {
                        showingPreview = false
                        confirmTrim()
                    },
                    onRetry: {
                        showingPreview = false
                        previewCutout()
                    },
                    onCancel: {
                        showingPreview = false
                        framePreview = nil
                    }
                )
            }
        }
    }
    
    private func loadVideoInfo() {
        Task {
            if let info = await videoProcessor.getVideoInfo(from: videoURL) {
                await MainActor.run {
                    self.videoDuration = info.duration
                    self.endTime = min(maxDuration, info.duration)
                    setupPlayer()
                }
            }
        }
    }
    
    private func setupPlayer() {
        player = AVPlayer(url: videoURL)
        
        // 监听播放结束
        NotificationCenter.default.addObserver(
            forName: .AVPlayerItemDidPlayToEndTime,
            object: player?.currentItem,
            queue: .main
        ) { _ in
            isPlaying = false
            seekToTime(startTime)
        }
    }
    
    private func togglePlayback() {
        guard let player = player else { return }
        
        if isPlaying {
            player.pause()
        } else {
            // 确保从选择的开始时间播放
            seekToTime(startTime)
            player.play()
        }
        isPlaying.toggle()
    }
    
    private func seekToTime(_ time: Double) {
        let cmTime = CMTime(seconds: time, preferredTimescale: 600)
        player?.seek(to: cmTime)
    }
    
    private func updateEndTime() {
        endTime = min(startTime + maxDuration, videoDuration)
    }
    
    private func updateStartTime() {
        startTime = max(0, endTime - maxDuration)
    }

    private func previewCutout() {
        guard endTime - startTime <= maxDuration else {
            alertMessage = "选择的时间段不能超过\(Int(maxDuration))秒"
            showingAlert = true
            return
        }

        guard endTime > startTime else {
            alertMessage = "结束时间必须大于开始时间"
            showingAlert = true
            return
        }

        showingProcessing = true

        Task {
            // 预览中间时间点的帧
            let previewTime = startTime + (endTime - startTime) / 2
            if let preview = await videoProcessor.previewFrameCutout(from: videoURL, at: previewTime) {
                await MainActor.run {
                    showingProcessing = false
                    framePreview = preview
                    showingPreview = true
                }
            } else {
                await MainActor.run {
                    showingProcessing = false
                    alertMessage = videoProcessor.errorMessage ?? "预览失败"
                    showingAlert = true
                }
            }
        }
    }

    private func confirmTrim() {
        guard endTime - startTime <= maxDuration else {
            alertMessage = "选择的时间段不能超过\(Int(maxDuration))秒"
            showingAlert = true
            return
        }

        guard endTime > startTime else {
            alertMessage = "结束时间必须大于开始时间"
            showingAlert = true
            return
        }

        showingProcessing = true

        Task {
            if let frameSequence = await videoProcessor.processVideoToFrameSequence(
                from: videoURL,
                startTime: startTime,
                endTime: endTime
            ) {
                await MainActor.run {
                    showingProcessing = false
                    onComplete(frameSequence)
                }
            } else {
                await MainActor.run {
                    showingProcessing = false
                    alertMessage = videoProcessor.errorMessage ?? "视频处理失败"
                    showingAlert = true
                }
            }
        }
    }
    
    private func formatTime(_ time: Double) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

#Preview {
    VideoTrimView(
        videoURL: URL(string: "https://example.com/video.mp4")!,
        onComplete: { _ in },
        onCancel: { }
    )
}
